#!/bin/bash

# Build script for SRAMS (Student Record and Attendance Management System)
# This script compiles and runs the application without Maven

echo "=== SRAMS Build Script ==="

# Create directories
echo "Creating build directories..."
mkdir -p build/classes
mkdir -p lib

# Download dependencies if they don't exist
echo "Checking dependencies..."

# SQLite JDBC
if [ ! -f "lib/sqlite-jdbc-********.jar" ]; then
    echo "Downloading SQLite JDBC driver..."
    curl -L -o lib/sqlite-jdbc-********.jar https://repo1.maven.org/maven2/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar
fi

# BCrypt
if [ ! -f "lib/jbcrypt-0.4.jar" ]; then
    echo "Downloading BCrypt library..."
    curl -L -o lib/jbcrypt-0.4.jar https://repo1.maven.org/maven2/org/mindrot/jbcrypt/0.4/jbcrypt-0.4.jar
fi

# Set classpath
CLASSPATH="lib/sqlite-jdbc-********.jar:lib/jbcrypt-0.4.jar:build/classes"

# Compile Java files
echo "Compiling Java files..."
find src/main/java -name "*.java" > sources.txt
javac -cp "$CLASSPATH" -d build/classes @sources.txt

if [ $? -eq 0 ]; then
    echo "✓ Compilation successful"
else
    echo "✗ Compilation failed"
    exit 1
fi

# Copy resources
echo "Copying resources..."
cp -r src/main/resources/* build/classes/ 2>/dev/null || true

# Create executable scripts
echo "Creating run scripts..."

# GUI version (default)
cat > run-gui.sh << 'EOF'
#!/bin/bash
CLASSPATH="lib/sqlite-jdbc-********.jar:lib/jbcrypt-0.4.jar:build/classes"
java -cp "$CLASSPATH" com.srams.SRAMSGUIApplication
EOF

# Console version
cat > run-console.sh << 'EOF'
#!/bin/bash
CLASSPATH="lib/sqlite-jdbc-********.jar:lib/jbcrypt-0.4.jar:build/classes"
java -cp "$CLASSPATH" com.srams.SRAMSApplication
EOF

# Default run script (GUI)
cat > run.sh << 'EOF'
#!/bin/bash
CLASSPATH="lib/sqlite-jdbc-********.jar:lib/jbcrypt-0.4.jar:build/classes"
java -cp "$CLASSPATH" com.srams.SRAMSGUIApplication
EOF

chmod +x run-gui.sh run-console.sh run.sh

# Create test script
echo "Creating test script..."
cat > test.sh << 'EOF'
#!/bin/bash
CLASSPATH="lib/sqlite-jdbc-********.jar:lib/jbcrypt-0.4.jar:build/classes"

# Compile test files
find src/test/java -name "*.java" > test_sources.txt
javac -cp "$CLASSPATH" -d build/classes @test_sources.txt

if [ $? -eq 0 ]; then
    echo "✓ Test compilation successful"
    echo "Running basic tests..."
    java -cp "$CLASSPATH" com.srams.BasicTest
    echo ""
    echo "Running file upload tests..."
    java -cp "$CLASSPATH" com.srams.FileUploadTest
    echo ""
    echo "Running GUI tests..."
    java -cp "$CLASSPATH" com.srams.GUITest
else
    echo "✗ Test compilation failed"
fi
EOF

chmod +x test.sh

# Clean up
rm -f sources.txt test_sources.txt

echo ""
echo "=== Build Complete ==="
echo "To run GUI version: ./run.sh or ./run-gui.sh"
echo "To run console version: ./run-console.sh"
echo "To run tests: ./test.sh"
echo "To clean build: rm -rf build/"
