package com.srams;

import com.srams.dao.AttendanceDAO;
import com.srams.dao.StudentDAO;
import com.srams.model.Attendance;
import com.srams.model.Attendance.AttendanceStatus;
import com.srams.model.Student;
import com.srams.service.AuthenticationService;
import com.srams.util.DatabaseManager;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

/**
 * Main application class for Student Record and Attendance Management System
 */
public class SRAMSApplication {
    private final Scanner scanner;
    private final AuthenticationService authService;
    private final StudentDAO studentDAO;
    private final AttendanceDAO attendanceDAO;
    private final DateTimeFormatter dateFormatter;

    public SRAMSApplication() {
        this.scanner = new Scanner(System.in);
        this.authService = new AuthenticationService();
        this.studentDAO = new StudentDAO();
        this.attendanceDAO = new AttendanceDAO();
        this.dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    }

    public static void main(String[] args) {
        System.out.println("=== Student Record and Attendance Management System ===");
        System.out.println("Initializing database...");
        
        // Test database connection
        DatabaseManager dbManager = DatabaseManager.getInstance();
        if (!dbManager.testConnection()) {
            System.err.println("Failed to connect to database. Exiting...");
            return;
        }
        
        SRAMSApplication app = new SRAMSApplication();
        app.run();
    }

    public void run() {
        System.out.println("Database initialized successfully!");
        System.out.println("\nDefault admin credentials:");
        System.out.println("Username: admin");
        System.out.println("Password: admin123");
        
        while (true) {
            if (!authService.isLoggedIn()) {
                showAuthMenu();
            } else {
                showMainMenu();
            }
        }
    }

    private void showAuthMenu() {
        System.out.println("\n=== Authentication Menu ===");
        System.out.println("1. Login");
        System.out.println("2. Register");
        System.out.println("3. Exit");
        System.out.print("Choose an option: ");

        int choice = getIntInput();
        switch (choice) {
            case 1 -> handleLogin();
            case 2 -> handleRegister();
            case 3 -> {
                System.out.println("Thank you for using SRAMS. Goodbye!");
                DatabaseManager.getInstance().closeConnection();
                System.exit(0);
            }
            default -> System.out.println("Invalid option. Please try again.");
        }
    }

    private void showMainMenu() {
        System.out.println("\n=== Main Menu ===");
        System.out.println("1. Student Management");
        System.out.println("2. Attendance Management");
        System.out.println("3. Reports");
        System.out.println("4. Change Password");
        System.out.println("5. Logout");
        System.out.print("Choose an option: ");

        int choice = getIntInput();
        switch (choice) {
            case 1 -> showStudentMenu();
            case 2 -> showAttendanceMenu();
            case 3 -> showReportsMenu();
            case 4 -> handleChangePassword();
            case 5 -> authService.logout();
            default -> System.out.println("Invalid option. Please try again.");
        }
    }

    private void handleLogin() {
        System.out.print("Username: ");
        String username = scanner.nextLine().trim();
        System.out.print("Password: ");
        String password = scanner.nextLine();
        
        authService.login(username, password);
    }

    private void handleRegister() {
        System.out.print("Username: ");
        String username = scanner.nextLine().trim();
        System.out.print("Password: ");
        String password = scanner.nextLine();
        System.out.print("Email: ");
        String email = scanner.nextLine().trim();
        
        authService.register(username, password, email);
    }

    private void handleChangePassword() {
        System.out.print("Current Password: ");
        String currentPassword = scanner.nextLine();
        System.out.print("New Password: ");
        String newPassword = scanner.nextLine();
        
        authService.changePassword(currentPassword, newPassword);
    }

    private void showStudentMenu() {
        System.out.println("\n=== Student Management ===");
        System.out.println("1. Add Student");
        System.out.println("2. View All Students");
        System.out.println("3. Search Student");
        System.out.println("4. Update Student");
        System.out.println("5. Delete Student");
        System.out.println("6. Back to Main Menu");
        System.out.print("Choose an option: ");

        int choice = getIntInput();
        switch (choice) {
            case 1 -> addStudent();
            case 2 -> viewAllStudents();
            case 3 -> searchStudent();
            case 4 -> updateStudent();
            case 5 -> deleteStudent();
            case 6 -> { /* Return to main menu */ }
            default -> System.out.println("Invalid option. Please try again.");
        }
    }

    private void addStudent() {
        System.out.println("\n=== Add New Student ===");
        System.out.print("Roll Number: ");
        String rollNumber = scanner.nextLine().trim();
        
        if (studentDAO.rollNumberExists(rollNumber)) {
            System.out.println("Roll number already exists!");
            return;
        }
        
        System.out.print("First Name: ");
        String firstName = scanner.nextLine().trim();
        System.out.print("Last Name: ");
        String lastName = scanner.nextLine().trim();
        System.out.print("Email (optional): ");
        String email = scanner.nextLine().trim();
        System.out.print("Phone (optional): ");
        String phone = scanner.nextLine().trim();
        System.out.print("Department: ");
        String department = scanner.nextLine().trim();
        System.out.print("Year of Study: ");
        int year = getIntInput();
        System.out.print("Date of Birth (yyyy-mm-dd, optional): ");
        String dobStr = scanner.nextLine().trim();
        System.out.print("Address (optional): ");
        String address = scanner.nextLine().trim();

        Student student = new Student(rollNumber, firstName, lastName, department, year);
        if (!email.isEmpty()) student.setEmail(email);
        if (!phone.isEmpty()) student.setPhone(phone);
        if (!address.isEmpty()) student.setAddress(address);
        
        if (!dobStr.isEmpty()) {
            try {
                student.setDateOfBirth(LocalDate.parse(dobStr, dateFormatter));
            } catch (DateTimeParseException e) {
                System.out.println("Invalid date format. Date of birth not set.");
            }
        }

        if (studentDAO.addStudent(student)) {
            System.out.println("Student added successfully!");
        } else {
            System.out.println("Failed to add student.");
        }
    }

    private void viewAllStudents() {
        System.out.println("\n=== All Students ===");
        List<Student> students = studentDAO.getAllStudents();
        
        if (students.isEmpty()) {
            System.out.println("No students found.");
            return;
        }

        System.out.printf("%-5s %-15s %-20s %-15s %-5s%n", "ID", "Roll Number", "Name", "Department", "Year");
        System.out.println("-".repeat(65));
        
        for (Student student : students) {
            System.out.printf("%-5d %-15s %-20s %-15s %-5d%n",
                student.getId(),
                student.getRollNumber(),
                student.getFullName(),
                student.getDepartment(),
                student.getYearOfStudy()
            );
        }
    }

    private void searchStudent() {
        System.out.println("\n=== Search Student ===");
        System.out.print("Enter Roll Number: ");
        String rollNumber = scanner.nextLine().trim();
        
        Student student = studentDAO.getStudentByRollNumber(rollNumber);
        if (student != null) {
            displayStudentDetails(student);
        } else {
            System.out.println("Student not found.");
        }
    }

    private void displayStudentDetails(Student student) {
        System.out.println("\n=== Student Details ===");
        System.out.println("ID: " + student.getId());
        System.out.println("Roll Number: " + student.getRollNumber());
        System.out.println("Name: " + student.getFullName());
        System.out.println("Email: " + (student.getEmail() != null ? student.getEmail() : "N/A"));
        System.out.println("Phone: " + (student.getPhone() != null ? student.getPhone() : "N/A"));
        System.out.println("Department: " + student.getDepartment());
        System.out.println("Year of Study: " + student.getYearOfStudy());
        System.out.println("Date of Birth: " + (student.getDateOfBirth() != null ? student.getDateOfBirth() : "N/A"));
        System.out.println("Address: " + (student.getAddress() != null ? student.getAddress() : "N/A"));
    }

    private void updateStudent() {
        System.out.println("\n=== Update Student ===");
        System.out.print("Enter Roll Number: ");
        String rollNumber = scanner.nextLine().trim();

        Student student = studentDAO.getStudentByRollNumber(rollNumber);
        if (student == null) {
            System.out.println("Student not found.");
            return;
        }

        System.out.println("Current details:");
        displayStudentDetails(student);

        System.out.println("\nEnter new details (press Enter to keep current value):");

        System.out.print("First Name [" + student.getFirstName() + "]: ");
        String firstName = scanner.nextLine().trim();
        if (!firstName.isEmpty()) student.setFirstName(firstName);

        System.out.print("Last Name [" + student.getLastName() + "]: ");
        String lastName = scanner.nextLine().trim();
        if (!lastName.isEmpty()) student.setLastName(lastName);

        System.out.print("Email [" + (student.getEmail() != null ? student.getEmail() : "N/A") + "]: ");
        String email = scanner.nextLine().trim();
        if (!email.isEmpty()) student.setEmail(email);

        System.out.print("Phone [" + (student.getPhone() != null ? student.getPhone() : "N/A") + "]: ");
        String phone = scanner.nextLine().trim();
        if (!phone.isEmpty()) student.setPhone(phone);

        System.out.print("Department [" + student.getDepartment() + "]: ");
        String department = scanner.nextLine().trim();
        if (!department.isEmpty()) student.setDepartment(department);

        System.out.print("Year of Study [" + student.getYearOfStudy() + "]: ");
        String yearStr = scanner.nextLine().trim();
        if (!yearStr.isEmpty()) {
            try {
                student.setYearOfStudy(Integer.parseInt(yearStr));
            } catch (NumberFormatException e) {
                System.out.println("Invalid year format. Year not updated.");
            }
        }

        System.out.print("Address [" + (student.getAddress() != null ? student.getAddress() : "N/A") + "]: ");
        String address = scanner.nextLine().trim();
        if (!address.isEmpty()) student.setAddress(address);

        if (studentDAO.updateStudent(student)) {
            System.out.println("Student updated successfully!");
        } else {
            System.out.println("Failed to update student.");
        }
    }

    private void deleteStudent() {
        System.out.println("\n=== Delete Student ===");
        System.out.print("Enter Roll Number: ");
        String rollNumber = scanner.nextLine().trim();

        Student student = studentDAO.getStudentByRollNumber(rollNumber);
        if (student == null) {
            System.out.println("Student not found.");
            return;
        }

        System.out.println("Student to delete:");
        displayStudentDetails(student);

        System.out.print("Are you sure you want to delete this student? (y/N): ");
        String confirm = scanner.nextLine().trim().toLowerCase();

        if ("y".equals(confirm) || "yes".equals(confirm)) {
            if (studentDAO.deleteStudent(student.getId())) {
                System.out.println("Student deleted successfully!");
            } else {
                System.out.println("Failed to delete student.");
            }
        } else {
            System.out.println("Delete operation cancelled.");
        }
    }

    private void showAttendanceMenu() {
        System.out.println("\n=== Attendance Management ===");
        System.out.println("1. Mark Attendance");
        System.out.println("2. View Student Attendance");
        System.out.println("3. View Attendance by Date");
        System.out.println("4. Back to Main Menu");
        System.out.print("Choose an option: ");

        int choice = getIntInput();
        switch (choice) {
            case 1 -> markAttendance();
            case 2 -> viewStudentAttendance();
            case 3 -> viewAttendanceByDate();
            case 4 -> { /* Return to main menu */ }
            default -> System.out.println("Invalid option. Please try again.");
        }
    }

    private void markAttendance() {
        System.out.println("\n=== Mark Attendance ===");
        System.out.print("Enter Roll Number: ");
        String rollNumber = scanner.nextLine().trim();

        Student student = studentDAO.getStudentByRollNumber(rollNumber);
        if (student == null) {
            System.out.println("Student not found.");
            return;
        }

        System.out.println("Student: " + student.getFullName());

        System.out.print("Date (yyyy-mm-dd) [Today]: ");
        String dateStr = scanner.nextLine().trim();
        LocalDate date = dateStr.isEmpty() ? LocalDate.now() :
                        parseDate(dateStr);

        if (date == null) {
            System.out.println("Invalid date format.");
            return;
        }

        System.out.print("Subject (optional): ");
        String subject = scanner.nextLine().trim();
        if (subject.isEmpty()) subject = "General";

        System.out.println("Attendance Status:");
        System.out.println("1. Present");
        System.out.println("2. Absent");
        System.out.println("3. Late");
        System.out.print("Choose status: ");

        int statusChoice = getIntInput();
        AttendanceStatus status;
        switch (statusChoice) {
            case 1 -> status = AttendanceStatus.PRESENT;
            case 2 -> status = AttendanceStatus.ABSENT;
            case 3 -> status = AttendanceStatus.LATE;
            default -> {
                System.out.println("Invalid status choice.");
                return;
            }
        }

        System.out.print("Remarks (optional): ");
        String remarks = scanner.nextLine().trim();

        Attendance attendance = new Attendance(student.getId(), date, status, subject);
        if (!remarks.isEmpty()) attendance.setRemarks(remarks);

        if (attendanceDAO.markAttendance(attendance)) {
            System.out.println("Attendance marked successfully!");
        } else {
            System.out.println("Failed to mark attendance.");
        }
    }

    private void viewStudentAttendance() {
        System.out.println("\n=== View Student Attendance ===");
        System.out.print("Enter Roll Number: ");
        String rollNumber = scanner.nextLine().trim();

        Student student = studentDAO.getStudentByRollNumber(rollNumber);
        if (student == null) {
            System.out.println("Student not found.");
            return;
        }

        System.out.println("Student: " + student.getFullName());

        List<Attendance> attendanceList = attendanceDAO.getStudentAttendance(student.getId());
        if (attendanceList.isEmpty()) {
            System.out.println("No attendance records found.");
            return;
        }

        System.out.printf("%-12s %-10s %-10s %-20s%n", "Date", "Status", "Subject", "Remarks");
        System.out.println("-".repeat(55));

        for (Attendance attendance : attendanceList) {
            System.out.printf("%-12s %-10s %-10s %-20s%n",
                attendance.getAttendanceDate(),
                attendance.getStatus(),
                attendance.getSubject() != null ? attendance.getSubject() : "N/A",
                attendance.getRemarks() != null ? attendance.getRemarks() : "N/A"
            );
        }

        // Show attendance statistics
        Map<String, Object> stats = attendanceDAO.getAttendanceStats(student.getId());
        System.out.println("\n=== Attendance Statistics ===");
        System.out.println("Total Days: " + stats.get("totalDays"));
        System.out.println("Present Days: " + stats.get("presentDays"));
        System.out.println("Absent Days: " + stats.get("absentDays"));
        System.out.println("Late Days: " + stats.get("lateDays"));
        System.out.println("Attendance Percentage: " + stats.get("attendancePercentage") + "%");
    }

    private void viewAttendanceByDate() {
        System.out.println("\n=== View Attendance by Date ===");
        System.out.print("Enter Date (yyyy-mm-dd): ");
        String dateStr = scanner.nextLine().trim();

        LocalDate date = parseDate(dateStr);
        if (date == null) {
            System.out.println("Invalid date format.");
            return;
        }

        List<Attendance> attendanceList = attendanceDAO.getAttendanceByDate(date);
        if (attendanceList.isEmpty()) {
            System.out.println("No attendance records found for " + date);
            return;
        }

        System.out.println("Attendance for " + date + ":");
        System.out.printf("%-15s %-20s %-10s %-10s %-20s%n", "Roll Number", "Student Name", "Status", "Subject", "Remarks");
        System.out.println("-".repeat(80));

        for (Attendance attendance : attendanceList) {
            Student student = studentDAO.getStudentById(attendance.getStudentId());
            if (student != null) {
                System.out.printf("%-15s %-20s %-10s %-10s %-20s%n",
                    student.getRollNumber(),
                    student.getFullName(),
                    attendance.getStatus(),
                    attendance.getSubject() != null ? attendance.getSubject() : "N/A",
                    attendance.getRemarks() != null ? attendance.getRemarks() : "N/A"
                );
            }
        }
    }

    private void showReportsMenu() {
        System.out.println("\n=== Reports ===");
        System.out.println("1. Student List by Department");
        System.out.println("2. Attendance Summary");
        System.out.println("3. Low Attendance Students");
        System.out.println("4. Back to Main Menu");
        System.out.print("Choose an option: ");

        int choice = getIntInput();
        switch (choice) {
            case 1 -> showStudentsByDepartment();
            case 2 -> showAttendanceSummary();
            case 3 -> showLowAttendanceStudents();
            case 4 -> { /* Return to main menu */ }
            default -> System.out.println("Invalid option. Please try again.");
        }
    }

    private void showStudentsByDepartment() {
        System.out.println("\n=== Students by Department ===");
        System.out.print("Enter Department: ");
        String department = scanner.nextLine().trim();

        List<Student> students = studentDAO.getStudentsByDepartment(department);
        if (students.isEmpty()) {
            System.out.println("No students found in " + department + " department.");
            return;
        }

        System.out.println("Students in " + department + " department:");
        System.out.printf("%-15s %-20s %-5s%n", "Roll Number", "Name", "Year");
        System.out.println("-".repeat(45));

        for (Student student : students) {
            System.out.printf("%-15s %-20s %-5d%n",
                student.getRollNumber(),
                student.getFullName(),
                student.getYearOfStudy()
            );
        }
    }

    private void showAttendanceSummary() {
        System.out.println("\n=== Attendance Summary ===");
        List<Student> students = studentDAO.getAllStudents();

        if (students.isEmpty()) {
            System.out.println("No students found.");
            return;
        }

        System.out.printf("%-15s %-20s %-10s %-10s %-15s%n", "Roll Number", "Name", "Total Days", "Present", "Percentage");
        System.out.println("-".repeat(75));

        for (Student student : students) {
            Map<String, Object> stats = attendanceDAO.getAttendanceStats(student.getId());
            System.out.printf("%-15s %-20s %-10s %-10s %-15s%%n",
                student.getRollNumber(),
                student.getFullName(),
                stats.get("totalDays"),
                stats.get("presentDays"),
                stats.get("attendancePercentage")
            );
        }
    }

    private void showLowAttendanceStudents() {
        System.out.println("\n=== Low Attendance Students (< 75%) ===");
        List<Student> students = studentDAO.getAllStudents();

        if (students.isEmpty()) {
            System.out.println("No students found.");
            return;
        }

        System.out.printf("%-15s %-20s %-15s%n", "Roll Number", "Name", "Percentage");
        System.out.println("-".repeat(55));

        boolean foundLowAttendance = false;
        for (Student student : students) {
            Map<String, Object> stats = attendanceDAO.getAttendanceStats(student.getId());
            double percentage = (Double) stats.get("attendancePercentage");

            if (percentage < 75.0 && (Integer) stats.get("totalDays") > 0) {
                System.out.printf("%-15s %-20s %-15s%%n",
                    student.getRollNumber(),
                    student.getFullName(),
                    percentage
                );
                foundLowAttendance = true;
            }
        }

        if (!foundLowAttendance) {
            System.out.println("No students with low attendance found.");
        }
    }

    private LocalDate parseDate(String dateStr) {
        try {
            return LocalDate.parse(dateStr, dateFormatter);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    private int getIntInput() {
        try {
            return Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            return -1;
        }
    }
}
