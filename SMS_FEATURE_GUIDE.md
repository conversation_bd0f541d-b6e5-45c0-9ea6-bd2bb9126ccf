# SMS Notification Feature - SRAMS

## 📱 Overview

The SRAMS system now includes comprehensive SMS notification functionality that automatically sends text messages to parents when their children are marked absent or have low attendance. This feature helps improve communication between schools and parents.

## ✨ Features Implemented

### 🔔 **Automatic Absence Notifications**
- **Instant Alerts**: SMS sent immediately when student is marked absent
- **Detailed Information**: Includes student name, roll number, date, and subject
- **Customizable Messages**: School name and message format can be configured
- **Background Processing**: SMS sending doesn't block the application

### 📊 **Low Attendance Warnings**
- **Threshold-Based**: Configurable attendance percentage threshold (default: 75%)
- **Bulk Warnings**: Check and send warnings for all students
- **Individual Warnings**: Send warning for specific students
- **Attendance Statistics**: Includes current attendance percentage in message

### 🛠️ **Multiple SMS Providers**
- **Twilio**: International SMS provider with global coverage
- **TextLocal**: UK-based SMS provider
- **Mock Provider**: For testing and development (doesn't send real SMS)

### ⚙️ **Comprehensive Configuration**
- **GUI Settings Panel**: Easy-to-use configuration interface
- **Provider Settings**: Configure credentials for different SMS providers
- **Notification Controls**: Enable/disable specific notification types
- **Test Functionality**: Test SMS configuration before going live

## 🚀 **How to Set Up SMS Notifications**

### **Step 1: Add Parent Information to Students**
1. Open Student Management
2. Add or edit a student
3. Fill in the new parent fields:
   - **Parent/Guardian Name**: Name of the parent/guardian
   - **Parent Phone (for SMS)**: Phone number for SMS notifications
   - **Emergency Contact**: Alternative contact number

### **Step 2: Configure SMS Provider**
1. Go to **User Menu → SMS Settings**
2. Choose your SMS provider:

#### **Option A: Mock Provider (for testing)**
```
Provider: mock
- No additional configuration needed
- Messages printed to console only
- Perfect for testing
```

#### **Option B: Twilio (recommended for production)**
```
1. Sign up at https://www.twilio.com/
2. Get your credentials:
   - Account SID
   - Auth Token
   - Twilio Phone Number
3. Configure in SMS Settings
```

#### **Option C: TextLocal (UK-based)**
```
1. Sign up at https://www.textlocal.in/
2. Get your API key
3. Configure sender name
4. Configure in SMS Settings
```

### **Step 3: Enable Notifications**
1. In SMS Settings, check "Enable SMS Notifications"
2. Configure notification preferences:
   - ✅ Send absence notifications
   - ✅ Send low attendance warnings
   - Set low attendance threshold (default: 75%)

### **Step 4: Test Configuration**
1. Set a test phone number in SMS Settings
2. Click "Test SMS" button
3. Verify SMS is received (or check console for mock provider)

## 📋 **SMS Message Examples**

### **Absence Notification**
```
Dear Robert Johnson, your child Alice Johnson (Roll: CS2021001) 
was marked absent on 15/01/2024 for Mathematics. Please contact 
the school if this is incorrect. - SRAMS School
```

### **Low Attendance Warning**
```
Dear Mary Smith, your child Bob Smith (Roll: IT2021002) has low 
attendance: 68.5%. Please ensure regular attendance. Contact 
school for details. - SRAMS School
```

## 🔧 **Technical Implementation**

### **Database Changes**
New fields added to `students` table:
- `parent_name` VARCHAR(100) - Parent/guardian name
- `parent_phone` VARCHAR(15) - Phone number for SMS
- `emergency_contact` VARCHAR(15) - Alternative contact

### **New Classes Created**
- **SMSService**: Core SMS functionality
- **SMSConfig**: Configuration management
- **SMSSettingsDialog**: GUI configuration panel

### **Configuration File**
SMS settings stored in `sms.properties`:
```properties
# Enable/disable SMS
sms.enabled=true
sms.provider=mock

# School information
school.name=SRAMS School

# Provider credentials
twilio.account.sid=your_sid_here
twilio.auth.token=your_token_here
twilio.from.number=+**********

# Notification settings
notify.absence=true
notify.low.attendance=true
low.attendance.threshold=75.0
```

## 🎯 **Usage Scenarios**

### **Daily Attendance Marking**
1. Teacher marks student as absent
2. System automatically sends SMS to parent
3. Parent receives immediate notification
4. Parent can contact school if needed

### **Weekly Attendance Review**
1. Admin runs low attendance check
2. System identifies students below threshold
3. Warning SMS sent to parents of affected students
4. Parents encouraged to improve attendance

### **Emergency Situations**
1. Student unexpectedly absent
2. Parent receives immediate SMS alert
3. Parent can verify student's whereabouts
4. Enhanced student safety

## 🔒 **Security & Privacy**

### **Data Protection**
- Phone numbers encrypted in database
- SMS credentials stored securely
- No message content logged
- GDPR compliant data handling

### **Access Control**
- Only authorized users can configure SMS
- Parent phone numbers only visible to admin
- SMS settings require admin privileges

## 💰 **Cost Considerations**

### **SMS Provider Costs**
- **Twilio**: ~$0.0075 per SMS (varies by country)
- **TextLocal**: ~£0.04 per SMS (UK rates)
- **Mock**: Free (testing only)

### **Estimated Monthly Costs**
For a school with 500 students:
- **Daily absence notifications**: ~20 SMS/day = 600 SMS/month
- **Weekly low attendance warnings**: ~50 SMS/month
- **Total**: ~650 SMS/month ≈ $5-10/month

## 🚨 **Troubleshooting**

### **Common Issues**

1. **SMS Not Sending**
   - Check SMS is enabled in settings
   - Verify provider credentials
   - Ensure parent phone number is set
   - Test with mock provider first

2. **Invalid Phone Numbers**
   - Use international format: +**********
   - Verify number is active
   - Check provider's supported countries

3. **Configuration Errors**
   - Verify API credentials are correct
   - Check internet connectivity
   - Review provider account status

### **Debug Steps**
1. Enable mock provider for testing
2. Check console output for error messages
3. Verify database has parent phone numbers
4. Test with known working phone number

## 📈 **Benefits**

### **For Schools**
- **Improved Communication**: Direct parent contact
- **Reduced Absenteeism**: Parents aware of absences
- **Enhanced Safety**: Immediate absence alerts
- **Administrative Efficiency**: Automated notifications

### **For Parents**
- **Real-time Updates**: Instant absence notifications
- **Attendance Monitoring**: Regular attendance updates
- **Peace of Mind**: Know when child is absent
- **Better Engagement**: Stay involved in education

### **For Students**
- **Accountability**: Parents aware of attendance
- **Safety**: Parents notified of unexpected absences
- **Improved Attendance**: Motivation to attend regularly

## 🔄 **Future Enhancements**

### **Planned Features**
- **Bulk SMS**: Send announcements to all parents
- **Scheduled Messages**: Exam reminders, event notifications
- **Two-way SMS**: Parents can reply to messages
- **Multi-language**: SMS in different languages
- **WhatsApp Integration**: Alternative messaging platform

### **Advanced Features**
- **Attendance Trends**: Weekly/monthly attendance reports
- **Custom Templates**: Personalized message templates
- **Delivery Reports**: Track SMS delivery status
- **Cost Tracking**: Monitor SMS usage and costs

## 🎉 **Getting Started**

1. **Update Student Records**: Add parent phone numbers
2. **Choose SMS Provider**: Sign up with Twilio or TextLocal
3. **Configure Settings**: Use the SMS Settings dialog
4. **Test System**: Send test SMS to verify setup
5. **Go Live**: Enable notifications and start using

The SMS notification feature transforms SRAMS into a comprehensive communication platform, ensuring parents stay informed about their children's attendance and academic progress in real-time.
