# SRAMS SMS Configuration
# Configure your SMS provider settings here

# General SMS Settings
sms.enabled=false
sms.provider=mock
school.name=SRAMS School
test.phone.number=+**********

# Twilio Configuration (if using Twilio)
# Sign up at https://www.twilio.com/ to get these credentials
twilio.account.sid=
twilio.auth.token=
twilio.from.number=

# TextLocal Configuration (if using TextLocal - UK based)
# Sign up at https://www.textlocal.in/ to get API key
textlocal.api.key=
textlocal.sender=SRAMS

# Notification Settings
notify.absence=true
notify.low.attendance=true
low.attendance.threshold=75.0

# SMS Provider Options:
# - mock: For testing (doesn't send real SMS, just prints to console)
# - twilio: Popular international SMS provider
# - textlocal: UK-based SMS provider

# To enable SMS notifications:
# 1. Set sms.enabled=true
# 2. Choose your provider (mock, twilio, or textlocal)
# 3. Configure the provider-specific settings above
# 4. Test with the test phone number

# Example Twilio Configuration:
# sms.enabled=true
# sms.provider=twilio
# twilio.account.sid=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# twilio.auth.token=your_auth_token_here
# twilio.from.number=+**********

# Example TextLocal Configuration:
# sms.enabled=true
# sms.provider=textlocal
# textlocal.api.key=your_api_key_here
# textlocal.sender=YourSchool
