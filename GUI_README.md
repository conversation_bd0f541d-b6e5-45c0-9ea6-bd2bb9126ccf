# SRAMS GUI - Student Record and Attendance Management System

## 🎨 GUI Version Overview

The SRAMS GUI provides a modern, user-friendly graphical interface built with Java Swing, offering all the functionality of the console version with enhanced usability and visual appeal.

## ✨ GUI Features

### 🖥️ **Modern Interface Design**
- **Clean, Professional Layout**: Modern color scheme with intuitive navigation
- **Responsive Design**: Adapts to different screen sizes
- **Consistent Styling**: Uniform look and feel across all components
- **System Integration**: Uses native look and feel for better OS integration

### 🔐 **Enhanced Authentication**
- **Login Dialog**: Secure login with username/password fields
- **Registration System**: New user registration with validation
- **Password Management**: Change password functionality
- **Session Management**: Automatic logout and session handling

### 📊 **Dashboard Overview**
- **System Statistics**: Real-time data on students and attendance
- **Quick Actions**: One-click access to common operations
- **Recent Activity**: Log of recent system activities
- **System Information**: Version and environment details

### 👥 **Student Management GUI**
- **Data Table**: Sortable, searchable student list
- **Form Dialogs**: User-friendly forms for adding/editing students
- **Search & Filter**: Real-time search and department filtering
- **Bulk Operations**: Select and manage multiple students
- **File Status**: Visual indicators for uploaded photos/marksheets

## 🚀 Running the GUI Application

### **Method 1: Using Maven (Recommended)**
```bash
# Run GUI version
mvn clean compile exec:java

# Or specifically run GUI
mvn exec:java@run-gui

# Run console version (if needed)
mvn exec:java@run-console
```

### **Method 2: Using JAR File**
```bash
# Build JAR
mvn clean package

# Run GUI application
java -jar target/student-record-attendance-system-1.0.0.jar
```

### **Method 3: Using Build Script**
```bash
# Build project
./build.sh

# Run GUI (modify run.sh to use SRAMSGUIApplication)
java -cp "lib/sqlite-jdbc-********.jar:lib/jbcrypt-0.4.jar:build/classes" com.srams.SRAMSGUIApplication
```

## 🎯 GUI Navigation Guide

### **1. Login Process**
1. Application starts with login dialog
2. Default credentials: `admin` / `admin123`
3. Option to register new users
4. Secure authentication with encrypted passwords

### **2. Main Window Layout**
```
┌─────────────────────────────────────────────────────┐
│ File  Student  Attendance  Reports  User     Help  │ ← Menu Bar
├─────────────────────────────────────────────────────┤
│                                                     │
│                Main Content Area                    │
│            (Dashboard/Student/etc.)                 │
│                                                     │
├─────────────────────────────────────────────────────┤
│ Status: Ready              Logged in as: admin     │ ← Status Bar
└─────────────────────────────────────────────────────┘
```

### **3. Menu System**
- **File**: Exit application
- **Student**: Manage Students, File Management
- **Attendance**: Manage Attendance (coming soon)
- **Reports**: View Reports (coming soon)
- **User**: Change Password, Logout
- **Help**: About, User Guide

## 📋 Student Management Features

### **Student List View**
- **Sortable Columns**: Click headers to sort data
- **Search Functionality**: Real-time search across all fields
- **Department Filter**: Filter by specific departments
- **File Status Indicators**: ✓/✗ for photos and marksheets
- **Double-click to Edit**: Quick access to student details

### **Student Form Dialog**
- **Validation**: Real-time field validation
- **Required Fields**: Clear marking of mandatory fields
- **Date Picker**: Easy date selection for birth dates
- **Department Dropdown**: Predefined department list
- **Auto-save**: Prevents data loss

### **Operations Available**
- ✅ **Add New Student**: Complete form with validation
- ✅ **Edit Student**: Modify existing student details
- ✅ **Delete Student**: Remove with confirmation dialog
- ✅ **Search Students**: Find by name, roll number, or department
- ✅ **Filter by Department**: Quick department-based filtering
- 🔄 **Refresh Data**: Reload latest information

## 🎨 Visual Design Elements

### **Color Scheme**
- **Primary Blue**: `#2980B9` - Main actions and headers
- **Success Green**: `#27AE60` - Positive actions
- **Warning Orange**: `#F39C12` - Caution actions
- **Danger Red**: `#E74C3C` - Delete/error actions
- **Light Gray**: `#ECF0F1` - Backgrounds
- **Dark Gray**: `#34495E` - Text

### **Typography**
- **Title Font**: Arial Bold 18px
- **Header Font**: Arial Bold 14px
- **Normal Font**: Arial Regular 12px
- **Small Font**: Arial Regular 10px

### **Interactive Elements**
- **Hover Effects**: Buttons change appearance on hover
- **Focus Indicators**: Clear focus states for accessibility
- **Loading States**: Progress indicators for long operations
- **Tooltips**: Helpful hints for form fields

## 🔧 Technical Implementation

### **Architecture**
```
GUI Layer (Swing Components)
├── SRAMSMainFrame (Main Window)
├── Panels (Dashboard, Student Management, etc.)
├── Dialogs (Login, Forms, etc.)
└── Utilities (GUIUtils, Styling)

Business Layer (Services & DAOs)
├── AuthenticationService
├── FileManagementService
├── StudentDAO, UserDAO, AttendanceDAO
└── Database Models

Data Layer (SQLite Database)
└── DatabaseManager
```

### **Key GUI Classes**
- **SRAMSGUIApplication**: Main entry point
- **SRAMSMainFrame**: Main application window
- **GUIUtils**: Common styling and utilities
- **LoginDialog**: User authentication
- **StudentManagementPanel**: Student CRUD operations
- **StudentFormDialog**: Add/edit student forms

## 🚧 Coming Soon Features

### **Attendance Management GUI**
- Visual calendar for date selection
- Bulk attendance marking
- Attendance statistics charts
- Export attendance reports

### **File Management GUI**
- Drag & drop file upload
- Image preview for photos
- Document viewer for marksheets
- Bulk file operations

### **Reports & Analytics GUI**
- Interactive charts and graphs
- Export to PDF/Excel
- Custom report builder
- Dashboard widgets

### **Advanced Features**
- **Dark Mode**: Toggle between light and dark themes
- **Keyboard Shortcuts**: Quick access to common operations
- **Multi-language Support**: Internationalization
- **Print Support**: Print student lists and reports
- **Backup/Restore**: Database backup functionality

## 🐛 Troubleshooting

### **Common Issues**

1. **Application Won't Start**
   - Check Java version (requires Java 11+)
   - Verify database file permissions
   - Check console for error messages

2. **Login Issues**
   - Use default credentials: admin/admin123
   - Check database connection
   - Try registering a new user

3. **Display Issues**
   - Update graphics drivers
   - Try different look and feel
   - Check screen resolution settings

4. **Performance Issues**
   - Close other applications
   - Increase Java heap size: `-Xmx512m`
   - Check available disk space

### **Debug Mode**
```bash
# Run with debug output
java -Djava.util.logging.level=ALL -jar srams.jar
```

## 📞 Support

For GUI-specific issues:
1. Check console output for error messages
2. Verify all dependencies are installed
3. Test with console version first
4. Report issues with screenshots

## 🎯 Benefits of GUI Version

### **User Experience**
- **Intuitive Interface**: No command-line knowledge required
- **Visual Feedback**: Clear status indicators and messages
- **Error Prevention**: Form validation prevents invalid data
- **Efficiency**: Faster navigation and data entry

### **Productivity**
- **Bulk Operations**: Select and manage multiple records
- **Quick Search**: Instant filtering and searching
- **Keyboard Shortcuts**: Power user features
- **Multi-tasking**: Multiple windows and dialogs

### **Accessibility**
- **Screen Reader Support**: Compatible with accessibility tools
- **Keyboard Navigation**: Full keyboard support
- **High Contrast**: Clear visual distinctions
- **Scalable Interface**: Adjustable font sizes

The GUI version of SRAMS provides a modern, efficient, and user-friendly interface while maintaining all the powerful features of the console application. It's designed for educational institutions that need a professional-grade student management system with an intuitive interface.
