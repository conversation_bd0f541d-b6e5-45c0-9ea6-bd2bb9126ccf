# Student Record and Attendance Management System (SRAMS)

A comprehensive Java application for managing student records and attendance with SQLite database integration.

## Features

### ✅ Core Features

#### 📁 Student Management
- Add new student (Name, Roll Number, Department, etc.)
- Edit/delete student details
- View all students
- Search students by roll number
- View students by department

#### 🔐 User Authentication
- User registration and login
- Password encryption using BCrypt
- Session management
- Change password functionality

#### 📆 Attendance Management
- Mark attendance (Present/Absent/Late) for a date
- View attendance for a student
- View attendance report for a specific date
- Calculate attendance percentage
- Subject-wise attendance tracking

#### 📊 Reports
- Student list by department
- Attendance summary for all students
- Low attendance students report (< 75%)
- Individual student attendance statistics

## Technology Stack

- **Language**: Java 11+
- **Database**: SQLite
- **Build Tool**: Maven
- **Dependencies**:
  - SQLite JDBC Driver
  - BCrypt for password hashing
  - JUnit for testing

## Project Structure

```
SRAMS/
├── src/
│   ├── main/
│   │   ├── java/com/srams/
│   │   │   ├── dao/           # Data Access Objects
│   │   │   ├── model/         # Entity classes
│   │   │   ├── service/       # Business logic
│   │   │   ├── util/          # Utility classes
│   │   │   └── SRAMSApplication.java  # Main application
│   │   └── resources/
│   │       └── schema.sql     # Database schema
│   └── test/
│       └── java/com/srams/
│           └── BasicTest.java # Basic functionality tests
├── pom.xml                    # Maven configuration
└── README.md
```

## Getting Started

### Prerequisites
- Java 11 or higher
- Maven 3.6 or higher

### Installation

1. Clone or download the project
2. Navigate to the project directory
3. Build the project:
   ```bash
   mvn clean compile
   ```

### Running the Application

#### Using Maven:
```bash
mvn exec:java -Dexec.mainClass="com.srams.SRAMSApplication"
```

#### Using compiled JAR:
```bash
mvn clean package
java -jar target/student-record-attendance-system-1.0.0.jar
```

### Running Tests

#### Basic functionality test:
```bash
mvn exec:java -Dexec.mainClass="com.srams.BasicTest"
```

#### Unit tests:
```bash
mvn test
```

## Usage

### Default Login Credentials
- **Username**: admin
- **Password**: admin123

### Main Menu Options

1. **Student Management**
   - Add, view, update, delete students
   - Search by roll number
   - View by department

2. **Attendance Management**
   - Mark daily attendance
   - View student attendance history
   - View attendance by date

3. **Reports**
   - Department-wise student lists
   - Attendance summaries
   - Low attendance alerts

4. **User Management**
   - Change password
   - Logout

## Database Schema

### Tables

#### users
- User authentication and authorization
- Encrypted password storage

#### students
- Complete student information
- Roll number, name, department, year, contact details

#### attendance
- Daily attendance records
- Status: Present/Absent/Late
- Subject-wise tracking
- Remarks and timestamps

## Key Features Implementation

### Object-Oriented Programming
- **Classes and Objects**: Student, User, Attendance models
- **Encapsulation**: Private fields with public getters/setters
- **Inheritance**: Common patterns in DAO classes
- **Polymorphism**: AttendanceStatus enum

### Database Operations (JDBC)
- **Connection Management**: Singleton DatabaseManager
- **CRUD Operations**: Complete Create, Read, Update, Delete
- **Prepared Statements**: SQL injection prevention
- **Transaction Management**: Consistent data operations

### Data Structures
- **ArrayList**: Dynamic student and attendance lists
- **HashMap**: Attendance statistics and reports
- **Enums**: AttendanceStatus for type safety

### Security Features
- **Password Hashing**: BCrypt encryption
- **Input Validation**: Data sanitization
- **SQL Injection Prevention**: Prepared statements

## Sample Usage Scenarios

### Adding a Student
1. Login to the system
2. Navigate to Student Management → Add Student
3. Enter student details (roll number, name, department, etc.)
4. System validates and saves the student

### Marking Attendance
1. Navigate to Attendance Management → Mark Attendance
2. Enter student roll number
3. Select date (default: today)
4. Choose attendance status (Present/Absent/Late)
5. Add optional subject and remarks

### Viewing Reports
1. Navigate to Reports
2. Choose from:
   - Student lists by department
   - Overall attendance summary
   - Students with low attendance

## Error Handling

- Database connection failures
- Invalid input validation
- Duplicate roll number prevention
- Date format validation
- Authentication failures

## Future Enhancements

- GUI interface using Swing/JavaFX
- Export reports to PDF/Excel
- Email notifications for low attendance
- Bulk attendance marking
- Academic year management
- Subject and course management
- Parent/guardian contact integration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is created for educational purposes and is free to use and modify.
