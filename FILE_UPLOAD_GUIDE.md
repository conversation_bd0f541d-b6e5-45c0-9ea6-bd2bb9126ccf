# File Upload Feature Guide - SRAMS

## Overview

The Student Record and Attendance Management System (SRAMS) now supports uploading and managing student photos and marksheets. This feature allows educational institutions to maintain complete digital records of their students.

## ✨ New Features Added

### 📸 Student Photo Upload
- **Supported Formats**: JPG, JPEG, PNG, GIF, BMP
- **Maximum Size**: 5 MB per photo
- **Storage**: Organized by student roll number with timestamps
- **Features**: Upload, view, replace, and delete photos

### 📄 Student Marksheet Upload
- **Supported Formats**: PDF, JPG, JPEG, PNG, DOC, DOCX
- **Maximum Size**: 10 MB per marksheet
- **Storage**: Organized by student roll number with timestamps
- **Features**: Upload, view, replace, and delete marksheets

## 🗂️ File Organization

Files are automatically organized in the following structure:
```
uploads/
├── photos/
│   ├── ROLL001_photo_20240115_143022.jpg
│   ├── ROLL002_photo_20240115_143045.png
│   └── ...
└── marksheets/
    ├── ROLL001_marksheet_20240115_143100.pdf
    ├── ROLL002_marksheet_20240115_143120.docx
    └── ...
```

## 🚀 How to Use

### Accessing File Management
1. Login to SRAMS
2. Navigate to **Student Management**
3. Select **File Management** (option 6)

### File Management Menu Options
```
=== File Management ===
1. Upload Student Photo
2. Upload Student Marksheet
3. View Student Files
4. Delete Student Photo
5. Delete Student Marksheet
6. Upload Guidelines
7. Back to Student Menu
```

### Uploading a Student Photo
1. Select **Upload Student Photo**
2. Enter the student's roll number
3. Provide the full path to the photo file
4. System validates and uploads the file
5. Database is updated with file path

**Example:**
```
=== Upload Student Photo ===
Enter Roll Number: *********
Student: John Doe
Enter photo file path: /home/<USER>/Documents/john_photo.jpg
Photo uploaded successfully: uploads/photos/*********_photo_20240115_143022.jpg
```

### Uploading a Student Marksheet
1. Select **Upload Student Marksheet**
2. Enter the student's roll number
3. Provide the full path to the marksheet file
4. System validates and uploads the file
5. Database is updated with file path

**Example:**
```
=== Upload Student Marksheet ===
Enter Roll Number: *********
Student: John Doe
Enter marksheet file path: /home/<USER>/Documents/john_marksheet.pdf
Marksheet uploaded successfully: uploads/marksheets/*********_marksheet_20240115_143100.pdf
```

### Viewing Student Files
1. Select **View Student Files**
2. Enter the student's roll number
3. System displays file information including:
   - File paths
   - File sizes
   - Upload status
   - File existence verification

**Example Output:**
```
=== Student Files Information ===
Student: John Doe
Roll Number: *********
Photo: uploads/photos/*********_photo_20240115_143022.jpg
Photo Size: 2.34 MB
Marksheet: uploads/marksheets/*********_marksheet_20240115_143100.pdf
Marksheet Size: 1.87 MB
```

### Deleting Files
1. Select appropriate delete option
2. Enter student roll number
3. Confirm deletion when prompted
4. File is removed from disk and database

## 📋 File Requirements

### Photo Requirements
- **Formats**: JPG, JPEG, PNG, GIF, BMP
- **Maximum Size**: 5 MB
- **Recommended**: Clear passport-size photo
- **Resolution**: Any (system accepts all resolutions)

### Marksheet Requirements
- **Formats**: PDF, JPG, JPEG, PNG, DOC, DOCX
- **Maximum Size**: 10 MB
- **Recommended**: PDF format for best quality and compatibility
- **Content**: Academic transcripts, certificates, grade sheets

## 🔧 Technical Implementation

### Database Changes
The `students` table has been enhanced with:
- `photo_path` VARCHAR(500) - Stores photo file path
- `marksheet_path` VARCHAR(500) - Stores marksheet file path

### New Classes Added
1. **FileUploadManager** - Handles file operations
2. **FileManagementService** - Business logic for file management
3. **Updated Student Model** - Added photo and marksheet fields
4. **Updated StudentDAO** - CRUD operations for file paths

### File Validation
- **Extension Check**: Only allowed file types accepted
- **Size Validation**: Files exceeding limits are rejected
- **Existence Check**: Source files must exist before upload
- **Path Validation**: Full file paths required

### Security Features
- **File Type Validation**: Prevents malicious file uploads
- **Size Limits**: Prevents storage abuse
- **Organized Storage**: Files stored in structured directories
- **Unique Naming**: Prevents file conflicts with timestamps

## 🛠️ Error Handling

### Common Error Messages
- `"Source file does not exist"` - Check file path
- `"Invalid file extension"` - Use supported formats
- `"File size exceeds maximum"` - Reduce file size
- `"Student not found"` - Verify roll number
- `"Failed to update student record"` - Database error

### Troubleshooting
1. **File Not Found**: Ensure full absolute path is provided
2. **Permission Denied**: Check file and directory permissions
3. **Size Too Large**: Compress or resize files before upload
4. **Invalid Format**: Convert to supported format
5. **Database Error**: Check database connection

## 📊 File Statistics

Use the upload statistics feature to monitor:
- Total photos uploaded
- Total marksheets uploaded
- Storage usage
- File organization

Access via the FileUploadManager's `printUploadStats()` method.

## 🔄 File Replacement

When uploading a new file for a student who already has one:
1. System prompts for confirmation
2. Old file is automatically deleted
3. New file is uploaded and linked
4. Database is updated with new path

## 💡 Best Practices

### For Administrators
1. **Regular Backups**: Backup the uploads directory regularly
2. **Storage Monitoring**: Monitor disk space usage
3. **File Validation**: Verify uploaded files periodically
4. **Access Control**: Secure the uploads directory

### For Users
1. **File Naming**: Use descriptive names for source files
2. **Quality**: Upload clear, readable photos and documents
3. **Format Choice**: Use PDF for marksheets when possible
4. **Size Optimization**: Compress large files before upload

## 🚀 Future Enhancements

Potential improvements for future versions:
- **Bulk Upload**: Upload multiple files at once
- **Image Preview**: View photos within the application
- **Document Viewer**: View PDFs and documents inline
- **Cloud Storage**: Integration with cloud storage services
- **File Versioning**: Keep history of uploaded files
- **Automated Backup**: Scheduled backups of uploaded files

## 📞 Support

For issues with file uploads:
1. Check file format and size requirements
2. Verify file paths are correct and accessible
3. Ensure sufficient disk space
4. Check database connectivity
5. Review error messages for specific guidance

The file upload feature enhances SRAMS by providing complete digital record management for educational institutions, making it easier to maintain comprehensive student profiles with visual identification and academic documentation.
