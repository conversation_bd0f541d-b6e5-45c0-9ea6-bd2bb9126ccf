#!/bin/bash

# Simple validation script to check if the code structure is correct

echo "=== SRAMS Code Validation ==="

# Check if all required files exist
echo "Checking file structure..."

required_files=(
    "src/main/java/com/srams/SRAMSApplication.java"
    "src/main/java/com/srams/model/Student.java"
    "src/main/java/com/srams/model/User.java"
    "src/main/java/com/srams/model/Attendance.java"
    "src/main/java/com/srams/dao/StudentDAO.java"
    "src/main/java/com/srams/dao/UserDAO.java"
    "src/main/java/com/srams/dao/AttendanceDAO.java"
    "src/main/java/com/srams/service/AuthenticationService.java"
    "src/main/java/com/srams/util/DatabaseManager.java"
    "src/main/resources/schema.sql"
    "src/test/java/com/srams/BasicTest.java"
    "pom.xml"
    "README.md"
)

missing_files=0
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file (missing)"
        missing_files=$((missing_files + 1))
    fi
done

if [ $missing_files -eq 0 ]; then
    echo ""
    echo "✓ All required files are present!"
else
    echo ""
    echo "✗ $missing_files files are missing"
fi

# Check Java syntax (basic compilation check without dependencies)
echo ""
echo "Checking Java syntax..."

# Create a temporary directory for compilation test
mkdir -p temp_build

# Try to compile without external dependencies (will fail but shows syntax errors)
find src/main/java -name "*.java" | head -5 | while read file; do
    echo "Checking syntax: $file"
    javac -d temp_build "$file" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✓ Syntax OK"
    else
        echo "? Syntax check (may need dependencies)"
    fi
done

# Clean up
rm -rf temp_build

echo ""
echo "=== Validation Complete ==="
echo ""
echo "Project Structure Summary:"
echo "- Main Application: SRAMSApplication.java"
echo "- Models: Student, User, Attendance"
echo "- DAOs: StudentDAO, UserDAO, AttendanceDAO"
echo "- Services: AuthenticationService"
echo "- Utilities: DatabaseManager"
echo "- Database: SQLite with schema.sql"
echo "- Build: Maven (pom.xml) + Shell script (build.sh)"
echo "- Documentation: README.md"
echo ""
echo "To build and run:"
echo "1. With Maven: mvn clean compile && mvn exec:java -Dexec.mainClass='com.srams.SRAMSApplication'"
echo "2. With shell script: ./build.sh && ./run.sh"
echo "3. To test: ./test.sh"
